"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { RegisterForm } from "@/components/auth/register-form";
import { useAuth } from "@/components/auth/auth-provider";

export default function RegisterPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, isLoading } = useAuth();
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const callbackUrl = searchParams.get("callbackUrl") || "/";

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      router.push(callbackUrl);
    }
  }, [isAuthenticated, isLoading, callbackUrl, router]);

  const handleSuccess = () => {
    setError(null);
    setSuccess("注册成功！正在跳转...");
    setTimeout(() => {
      router.push(callbackUrl);
    }, 1000);
  };

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
    setSuccess(null);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
          <p className="text-sm text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  if (isAuthenticated) {
    return null; // 将通过 useEffect 重定向
  }

  return (
    <div className="min-h-[calc(100dvh-4rem)] flex justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <h2 className="mb-6 text-center text-2xl font-extrabold text-gray-900">
            注册
          </h2>
          {error && (
            <div className="mb-4 p-4 rounded-md bg-red-50 border border-red-200">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {success && (
            <div className="mb-4 p-4 rounded-md bg-green-50 border border-green-200">
              <p className="text-sm text-green-600">{success}</p>
            </div>
          )}

          <RegisterForm
            callbackURL={callbackUrl}
            onSuccess={handleSuccess}
            onError={handleError}
          />

          <p className="mt-6 text-center text-sm text-gray-600">
            已有账号？{" "}
            <Link
              href="/login"
              className="text-blue-600 hover:text-blue-500"
            >
              立即登录
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
