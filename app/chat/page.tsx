'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useChat } from '@ai-sdk/react';
import { DefaultChatTransport } from 'ai';
import { ChatSidebar } from '@/components/chat/chat-sidebar';
import { ChatContent } from '@/components/chat/chat-content';
import { ChatInput } from '@/components/chat/chat-input';
import { useCreateChat, useCreateMessage } from '@/hooks/use-chats';
import { useAuth } from '@/hooks/use-auth';
import { ErrorBoundary } from '@/components/error-boundary';
import { Suspense } from 'react';

const models = [
  {
    name: 'GPT 4o',
    value: 'openai/gpt-4o',
  },
  {
    name: 'Deepseek R1',
    value: 'deepseek/deepseek-r1',
  },
];

function ChatPage() {
  const router = useRouter();
  const { isAuthenticated, isLoading } = useAuth();
  const [input, setInput] = useState('');
  const [model, setModel] = useState<string>(models[0].value);
  const [webSearch, setWebSearch] = useState(false);
  const [currentChatId, setCurrentChatId] = useState<string | null>(null);

  const createChat = useCreateChat();
  const createMessage = useCreateMessage();

  // 所有 hooks 必须在条件返回之前调用
  const { messages, sendMessage, status } = useChat({
    transport: new DefaultChatTransport({
      api: '/api/chat',
    }),
  });

  // 认证检查 - 在所有 hooks 调用之后
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          <p>加载中...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    router.push('/');
    return null;
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim()) return;

    try {
      let chatId = currentChatId;

      // 如果没有当前聊天，创建新聊天
      if (!chatId) {
        chatId = crypto.randomUUID();
        const title = input.slice(0, 50) + (input.length > 50 ? '...' : '');

        await createChat.mutateAsync({
          id: chatId,
          title,
        });

        setCurrentChatId(chatId);

        // 平滑跳转到新聊天页面
        router.push(`/chat/${chatId}`);
      }

      // 保存用户消息到数据库
      await createMessage.mutateAsync({
        chatId,
        role: 'user',
        parts: [{ type: 'text', text: input }],
        selectedModel: model,
      });

      // 发送消息到 AI
      sendMessage(
        { text: input },
        {
          body: {
            model,
            webSearch,
            chatId,
          },
        },
      );

      setInput('');
    } catch (error) {
      console.error('发送消息失败:', error);
    }
  };

  return (
    <ErrorBoundary>
      <div className="flex h-screen">
        {/* 左侧边栏 */}
        <div className="w-80 border-r">
          <ErrorBoundary>
            <Suspense fallback={<div className="p-4">加载中...</div>}>
              <ChatSidebar currentChatId={currentChatId || undefined} />
            </Suspense>
          </ErrorBoundary>
        </div>

        {/* 右侧聊天区域 */}
        <div className="flex-1 flex flex-col">
          {/* 聊天内容区域 */}
          <div className="flex-1 overflow-hidden">
            {messages.length > 0 ? (
              <ChatContent
                messages={messages.map(msg => ({
                  id: msg.id,
                  chatId: currentChatId || '',
                  role: msg.role,
                  parts: msg.parts,
                  attachments: [],
                  createdAt: new Date(),
                  parentMessageId: null,
                  isPartial: false,
                  selectedModel: model,
                  selectedTool: null,
                  annotations: null,
                }))}
                status={status}
                className="h-full"
              />
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center text-muted-foreground">
                  <h2 className="text-2xl font-semibold mb-2">开始新对话</h2>
                  <p>在下方输入框中输入消息开始对话</p>
                </div>
              </div>
            )}
          </div>

          {/* 输入区域 */}
          <div className="border-t p-4">
            <ChatInput
              input={input}
              onInputChange={setInput}
              onSubmit={handleSubmit}
              status={status}
              model={model}
              onModelChange={setModel}
              webSearch={webSearch}
              onWebSearchChange={setWebSearch}
              className="max-w-4xl mx-auto"
            />
          </div>
        </div>
      </div>
    </ErrorBoundary>
  );
}

export default ChatPage;