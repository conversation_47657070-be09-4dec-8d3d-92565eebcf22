'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useChat } from '@ai-sdk/react';
import { DefaultChatTransport } from 'ai';
import { ChatSidebar } from '@/components/chat/chat-sidebar';
import { ChatContent } from '@/components/chat/chat-content';
import { ChatInput } from '@/components/chat/chat-input';
import { useChatConditional as useChatData, useCreateMessage } from '@/hooks/use-chats';
import { useAuth } from '@/hooks/use-auth';
import { Suspense } from 'react';

const models = [
  {
    name: 'GPT 4o',
    value: 'openai/gpt-4o',
  },
  {
    name: 'Deepseek R1',
    value: 'deepseek/deepseek-r1',
  },
];

interface ChatPageProps {
  params: {
    chatId: string;
  };
}

function ChatPage({ params }: ChatPageProps) {
  const { chatId } = params;
  const router = useRouter();
  const { isAuthenticated, isLoading } = useAuth();
  const [input, setInput] = useState('');
  const [model, setModel] = useState<string>(models[0].value);
  const [webSearch, setWebSearch] = useState(false);

  const createMessage = useCreateMessage();

  // 获取聊天数据 - 必须在条件返回之前调用所有 hooks
  const { data: chatData } = useChatData(chatId, isAuthenticated && !isLoading);

  // 转换数据库消息为 UI 消息格式
  const initialMessages = chatData?.messages.map(msg => ({
    id: msg.id,
    role: msg.role as 'user' | 'assistant',
    parts: msg.parts as any[],
    createdAt: msg.createdAt,
  })) || [];

  const { sendMessage, status } = useChat({
    id: chatId,
    messages: initialMessages,
    transport: new DefaultChatTransport({
      api: '/api/chat',
    }),
  });

  // 认证检查 - 在所有 hooks 调用之后
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          <p>加载中...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    router.push('/');
    return null;
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim()) return;

    try {
      // 保存用户消息到数据库
      await createMessage.mutateAsync({
        chatId,
        role: 'user',
        parts: [{ type: 'text', text: input }],
        selectedModel: model,
      });

      // 发送消息到 AI
      sendMessage(
        { text: input },
        {
          body: {
            model,
            webSearch,
            chatId,
          },
        },
      );

      setInput('');
    } catch (error) {
      console.error('发送消息失败:', error);
    }
  };

  return (
    <div className="flex h-screen">
      {/* 左侧边栏 */}
      <div className="w-80 border-r">
        <Suspense fallback={<div className="p-4">加载中...</div>}>
          <ChatSidebar currentChatId={chatId} />
        </Suspense>
      </div>

      {/* 右侧聊天区域 */}
      <div className="flex-1 flex flex-col">
        {/* 聊天内容区域 */}
        <div className="flex-1 overflow-hidden">
          <ChatContent
            messages={chatData?.messages || []}
            status={status}
            className="h-full"
          />
        </div>

        {/* 输入区域 */}
        <div className="border-t p-4">
          <ChatInput
            input={input}
            onInputChange={setInput}
            onSubmit={handleSubmit}
            status={status}
            model={model}
            onModelChange={setModel}
            webSearch={webSearch}
            onWebSearchChange={setWebSearch}
            className="max-w-4xl mx-auto"
          />
        </div>
      </div>
    </div>
  );
}

export default ChatPage;