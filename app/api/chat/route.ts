import { streamText, UIMessage, convertToModelMessages } from 'ai';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { chat, message } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

// Allow streaming responses up to 30 seconds
export const maxDuration = 30;

export async function POST(req: Request) {
  try {
    // 验证用户认证
    const session = await auth.api.getSession({
      headers: req.headers,
    });

    if (!session?.user) {
      return new Response('Unauthorized', { status: 401 });
    }

    const {
      messages,
      model,
      webSearch,
      chatId,
    }: {
      messages: UIMessage[];
      model: string;
      webSearch: boolean;
      chatId?: string;
    } = await req.json();

    // 如果提供了 chatId，验证聊天所有权
    if (chatId) {
      const chatData = await db
        .select()
        .from(chat)
        .where(
          and(
            eq(chat.id, chatId),
            eq(chat.userId, session.user.id)
          )
        )
        .limit(1);

      if (chatData.length === 0) {
        return new Response('Chat not found or unauthorized', { status: 404 });
      }
    }

    const result = streamText({
      model: webSearch ? 'perplexity/sonar' : model,
      messages: convertToModelMessages(messages),
      system:
        'You are a helpful assistant that can answer questions and help with tasks',
      onFinish: async (result) => {
        // 如果提供了 chatId，保存消息到数据库
        if (chatId && result.text) {
          try {
            // 保存助手回复
            await db.insert(message).values({
              chatId,
              role: 'assistant',
              parts: [{ type: 'text', text: result.text }],
              attachments: [],
              createdAt: new Date(),
              selectedModel: model,
            });

            // 更新聊天的最后更新时间
            await db
              .update(chat)
              .set({ updatedAt: new Date() })
              .where(eq(chat.id, chatId));
          } catch (error) {
            console.error('保存消息失败:', error);
          }
        }
      },
    });

    // send sources and reasoning back to the client
    return result.toUIMessageStreamResponse({
      sendSources: true,
      sendReasoning: true,
    });
  } catch (error) {
    console.error('Chat API error:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}