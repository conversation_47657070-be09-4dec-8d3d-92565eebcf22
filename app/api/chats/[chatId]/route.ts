import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { chat, message } from '@/lib/db/schema';
import { eq, and, asc } from 'drizzle-orm';

export async function GET(
  request: NextRequest,
  { params }: { params: { chatId: string } }
) {
  try {
    // 验证用户认证
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { chatId } = params;

    // 获取聊天信息
    const chatData = await db
      .select()
      .from(chat)
      .where(
        and(
          eq(chat.id, chatId),
          eq(chat.userId, session.user.id)
        )
      )
      .limit(1);

    if (chatData.length === 0) {
      return NextResponse.json(
        { error: '聊天不存在或无权访问' },
        { status: 404 }
      );
    }

    // 获取聊天的所有消息
    const messages = await db
      .select()
      .from(message)
      .where(eq(message.chatId, chatId))
      .orderBy(asc(message.createdAt));

    return NextResponse.json({
      chat: chatData[0],
      messages,
    });
  } catch (error) {
    console.error('获取聊天详情失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { chatId: string } }
) {
  try {
    // 验证用户认证
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { chatId } = params;
    const body = await request.json();
    const { title, visibility, isPinned } = body;

    // 验证聊天所有权
    const chatData = await db
      .select()
      .from(chat)
      .where(
        and(
          eq(chat.id, chatId),
          eq(chat.userId, session.user.id)
        )
      )
      .limit(1);

    if (chatData.length === 0) {
      return NextResponse.json(
        { error: '聊天不存在或无权访问' },
        { status: 404 }
      );
    }

    // 更新聊天信息
    const updatedChat = await db
      .update(chat)
      .set({
        ...(title && { title }),
        ...(visibility && { visibility }),
        ...(isPinned !== undefined && { isPinned }),
        updatedAt: new Date(),
      })
      .where(eq(chat.id, chatId))
      .returning();

    return NextResponse.json({ chat: updatedChat[0] });
  } catch (error) {
    console.error('更新聊天失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { chatId: string } }
) {
  try {
    // 验证用户认证
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { chatId } = params;

    // 验证聊天所有权
    const chatData = await db
      .select()
      .from(chat)
      .where(
        and(
          eq(chat.id, chatId),
          eq(chat.userId, session.user.id)
        )
      )
      .limit(1);

    if (chatData.length === 0) {
      return NextResponse.json(
        { error: '聊天不存在或无权访问' },
        { status: 404 }
      );
    }

    // 删除聊天（消息会因为外键约束自动删除）
    await db
      .delete(chat)
      .where(eq(chat.id, chatId));

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('删除聊天失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
