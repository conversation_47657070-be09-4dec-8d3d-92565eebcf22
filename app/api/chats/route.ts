import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { chat, message } from '@/lib/db/schema';
import { desc, eq, sql } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    // 验证用户认证
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 获取用户的所有聊天，按更新时间倒序排列
    const chats = await db
      .select({
        id: chat.id,
        title: chat.title,
        createdAt: chat.createdAt,
        updatedAt: chat.updatedAt,
        visibility: chat.visibility,
        isPinned: chat.isPinned,
        // 获取最后一条消息的预览
        lastMessage: sql<string>`(
          SELECT COALESCE(
            (SELECT (parts->0->>'text')::text 
             FROM ${message} 
             WHERE ${message.chatId} = ${chat.id} 
               AND ${message.role} = 'user'
             ORDER BY ${message.createdAt} DESC 
             LIMIT 1),
            ''
          )
        )`,
        messageCount: sql<number>`(
          SELECT COUNT(*)::int 
          FROM ${message} 
          WHERE ${message.chatId} = ${chat.id}
        )`
      })
      .from(chat)
      .where(eq(chat.userId, session.user.id))
      .orderBy(desc(chat.isPinned), desc(chat.updatedAt));

    return NextResponse.json({ chats });
  } catch (error) {
    console.error('获取聊天列表失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // 验证用户认证
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { id, title, visibility = 'private' } = body;

    if (!id || !title) {
      return NextResponse.json(
        { error: '缺少必要参数' },
        { status: 400 }
      );
    }

    // 创建新的聊天
    const newChat = await db
      .insert(chat)
      .values({
        id,
        title,
        userId: session.user.id,
        visibility,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    return NextResponse.json({ chat: newChat[0] });
  } catch (error) {
    console.error('创建聊天失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
