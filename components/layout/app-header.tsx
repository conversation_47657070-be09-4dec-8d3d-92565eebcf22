"use client";

import Link from "next/link";
import { Button } from "@/components/ui/button";
import { UserMenu } from "@/components/auth/user-menu";
import { useAuth } from "@/components/auth/auth-provider";
import { MessageSquare, Plus } from "lucide-react";

export function AppHeader() {
  const { user, isAuthenticated, isLoading } = useAuth();

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center">
        {/* Logo */}
        <div className="mr-4 flex">
          <Link href="/" className="mr-6 flex items-center space-x-2">
            <MessageSquare className="h-6 w-6" />
            <span className="hidden font-bold sm:inline-block">
              Prompt Repo
            </span>
          </Link>
        </div>

        {/* 导航菜单 */}
        <div className="flex flex-1 items-center justify-between space-x-2 md:justify-end">
          <nav className="flex items-center space-x-6 text-sm font-medium">
            {isAuthenticated && (
              <>
                <Link
                  href="/chat"
                  className="transition-colors hover:text-foreground/80 text-foreground/60"
                >
                  聊天
                </Link>
                <Link
                  href="/chat"
                  className="flex items-center space-x-1 transition-colors hover:text-foreground/80 text-foreground/60"
                >
                  <Plus className="h-4 w-4" />
                  <span>新建聊天</span>
                </Link>
              </>
            )}
          </nav>

          {/* 用户区域 */}
          <div className="flex items-center space-x-2">
            {isLoading ? (
              <div className="w-8 h-8 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin"></div>
            ) : isAuthenticated && user ? (
              <UserMenu user={{ ...user, image: user.image ?? undefined }} />
            ) : (
              <div className="flex items-center space-x-2">
                <Button variant="ghost" size="sm" asChild>
                  <Link href="/login">登录</Link>
                </Button>
                <Button size="sm" asChild>
                  <Link href="/register">注册</Link>
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}
