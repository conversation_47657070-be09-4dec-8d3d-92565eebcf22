"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/components/ui/toast";
import { authClient } from "@/lib/auth-client";
import { useState } from "react";
import Image from "next/image";

interface GoogleLoginButtonProps {
  callbackURL?: string;
  disabled?: boolean;
}

export function GoogleLoginButton({
  callbackURL = "/",
  disabled = false
}: GoogleLoginButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { addToast } = useToast();

  const handleGoogleLogin = async () => {
    try {
      setIsLoading(true);
      await authClient.signIn.social({
        provider: "google",
        callbackURL,
      });
    } catch (error) {
      console.error("Google 登录失败:", error);
      addToast({
        type: "error",
        title: "登录失败",
        description: "Google 登录过程中发生错误",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      onClick={handleGoogleLogin}
      disabled={disabled || isLoading}
      className="w-full"
      variant="outline"
    >
      {isLoading ? (
        <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
      ) : (
        <Image
          src="/images/google.svg"
          alt="Google"
          width={16}
          height={16}
          className="w-4 h-4"
        />
      )}
      使用 Google 登录
    </Button>
  );
}