"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Avatar } from "@/components/ui/avatar";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { authClient } from "@/lib/auth-client";
import { User, LogOut, Settings, UserCircle } from "lucide-react";

interface UserMenuProps {
  user: {
    id: string;
    name: string;
    email: string;
    image?: string;
  };
  onSignOut?: () => void;
}

export function UserMenu({ user, onSignOut }: UserMenuProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleSignOut = async () => {
    try {
      setIsLoading(true);
      await authClient.signOut();
      onSignOut?.();
    } catch (error) {
      console.error("登出失败:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <HoverCard>
      <HoverCardTrigger asChild>
        <Button variant="ghost" className="relative h-8 w-8 rounded-full">
          <Avatar className="h-8 w-8">
            {user.image ? (
              <img
                src={user.image}
                alt={user.name}
                className="h-full w-full rounded-full object-cover"
              />
            ) : (
              <div className="flex h-full w-full items-center justify-center rounded-full bg-muted">
                <UserCircle className="h-4 w-4" />
              </div>
            )}
          </Avatar>
        </Button>
      </HoverCardTrigger>
      <HoverCardContent className="w-80" align="end">
        <div className="space-y-4">
          {/* 用户信息 */}
          <div className="flex items-center space-x-4">
            <Avatar className="h-12 w-12">
              {user.image ? (
                <img
                  src={user.image}
                  alt={user.name}
                  className="h-full w-full rounded-full object-cover"
                />
              ) : (
                <div className="flex h-full w-full items-center justify-center rounded-full bg-muted">
                  <UserCircle className="h-6 w-6" />
                </div>
              )}
            </Avatar>
            <div className="space-y-1">
              <h4 className="text-sm font-semibold">{user.name}</h4>
              <p className="text-sm text-muted-foreground">{user.email}</p>
            </div>
          </div>

          {/* 菜单选项 */}
          <div className="space-y-2">
            <Button
              variant="ghost"
              className="w-full justify-start"
              onClick={() => {
                // 导航到个人资料页面
                window.location.href = "/profile";
              }}
            >
              <User className="mr-2 h-4 w-4" />
              个人资料
            </Button>
            
            <Button
              variant="ghost"
              className="w-full justify-start"
              onClick={() => {
                // 导航到设置页面
                window.location.href = "/settings";
              }}
            >
              <Settings className="mr-2 h-4 w-4" />
              设置
            </Button>
          </div>

          {/* 分隔线 */}
          <div className="border-t pt-2">
            <Button
              variant="ghost"
              className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
              onClick={handleSignOut}
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="mr-2 h-4 w-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
              ) : (
                <LogOut className="mr-2 h-4 w-4" />
              )}
              登出
            </Button>
          </div>
        </div>
      </HoverCardContent>
    </HoverCard>
  );
}
