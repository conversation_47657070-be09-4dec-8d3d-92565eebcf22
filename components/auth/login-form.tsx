"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/toast";
import { authClient } from "@/lib/auth-client";
import { Label } from "@/components/ui/label";
import { GitHubLoginButton } from "./github-login-button";
import { GoogleLoginButton } from "./google-login-button";

interface LoginFormProps {
  callbackURL?: string;
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export function LoginForm({
  callbackURL = "/",
  onSuccess,
  onError
}: LoginFormProps) {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [rememberMe, setRememberMe] = useState(true);
  const { addToast } = useToast();

  const handleEmailLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email || !password) {
      const errorMsg = "请填写邮箱和密码";
      onError?.(errorMsg);
      addToast({
        type: "error",
        title: "登录失败",
        description: errorMsg,
      });
      return;
    }

    try {
      setIsLoading(true);
      const { data, error } = await authClient.signIn.email({
        email,
        password,
        callbackURL,
        rememberMe,
      });

      if (error) {
        const errorMsg = error.message || "登录失败";
        onError?.(errorMsg);
        addToast({
          type: "error",
          title: "登录失败",
          description: errorMsg,
        });
      } else {
        addToast({
          type: "success",
          title: "登录成功",
          description: "正在跳转...",
        });
        onSuccess?.();
      }
    } catch (error) {
      const errorMsg = "登录过程中发生错误";
      onError?.(errorMsg);
      addToast({
        type: "error",
        title: "登录失败",
        description: errorMsg,
      });
      console.error("登录错误:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* 社交登录 */}
      <div className="space-y-3">
        <GitHubLoginButton
          callbackURL={callbackURL}
          disabled={isLoading}
        />
        <GoogleLoginButton
          callbackURL={callbackURL}
          disabled={isLoading}
        />
      </div>

      {/* 分隔线 */}
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">
            或
          </span>
        </div>
      </div>

      {/* 邮箱登录表单 */}
      <form onSubmit={handleEmailLogin} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="email">邮箱</Label>
          <Input
            id="email"
            type="email"
            placeholder="请输入邮箱"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            disabled={isLoading}
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="password">密码</Label>
          <Input
            id="password"
            type="password"
            placeholder="输入密码"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            disabled={isLoading}
            required
          />
        </div>

        <div className="flex items-center space-x-2">
          <input
            id="remember"
            type="checkbox"
            checked={rememberMe}
            onChange={(e) => setRememberMe(e.target.checked)}
            className="rounded border-gray-300"
          />
          <label htmlFor="remember" className="text-sm text-gray-600">
            记住我
          </label>
        </div>

        <Button
          type="submit"
          className="w-full"
          disabled={isLoading}
        >
          {isLoading ? (
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
              登录中...
            </div>
          ) : (
            "登录"
          )}
        </Button>
      </form>
    </div>
  );
}
