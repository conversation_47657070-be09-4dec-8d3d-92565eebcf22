"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/toast";
import { authClient } from "@/lib/auth-client";
import { Label } from "@/components/ui/label";
import { GitHubLoginButton } from "./github-login-button";
import { GoogleLoginButton } from "./google-login-button";

interface RegisterFormProps {
  callbackURL?: string;
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export function RegisterForm({
  callbackURL = "/",
  onSuccess,
  onError
}: RegisterFormProps) {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const { addToast } = useToast();

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const validateForm = () => {
    if (!formData.name.trim()) {
      onError?.("请输入姓名");
      return false;
    }
    if (!formData.email.trim()) {
      onError?.("请输入邮箱地址");
      return false;
    }
    if (formData.password.length < 8) {
      onError?.("密码至少需要8个字符");
      return false;
    }
    if (formData.password !== formData.confirmPassword) {
      onError?.("两次输入的密码不一致");
      return false;
    }
    return true;
  };

  const handleEmailRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      setIsLoading(true);
      const { data, error } = await authClient.signUp.email({
        name: formData.name,
        email: formData.email,
        password: formData.password,
        callbackURL,
      });

      if (error) {
        onError?.(error.message || "注册失败");
      } else {
        onSuccess?.();
      }
    } catch (error) {
      onError?.("注册过程中发生错误");
      console.error("注册错误:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* 社交登录 */}
      <div className="space-y-3">
        <GitHubLoginButton 
          callbackURL={callbackURL}
          disabled={isLoading}
        />
        <GoogleLoginButton 
          callbackURL={callbackURL}
          disabled={isLoading}
        />
      </div>

      {/* 分隔线 */}
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">
            或
          </span>
        </div>
      </div>

      {/* 邮箱注册表单 */}
      <form onSubmit={handleEmailRegister} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="name">昵称</Label>
          <Input
            id="name"
            type="text"
            placeholder="输入昵称"
            value={formData.name}
            onChange={(e) => handleInputChange("name", e.target.value)}
            disabled={isLoading}
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">邮箱</Label>
          <Input
            id="email"
            type="email"
            placeholder="请输入邮箱"
            value={formData.email}
            onChange={(e) => handleInputChange("email", e.target.value)}
            disabled={isLoading}
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="password">密码</Label>
          <Input
            id="password"
            type="password"
            placeholder="至少8个字符"
            value={formData.password}
            onChange={(e) => handleInputChange("password", e.target.value)}
            disabled={isLoading}
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="confirmPassword">确认密码</Label>
          <Input
            id="confirmPassword"
            type="password"
            placeholder="再次输入密码"
            value={formData.confirmPassword}
            onChange={(e) => handleInputChange("confirmPassword", e.target.value)}
            disabled={isLoading}
            required
          />
        </div>

        <Button 
          type="submit" 
          className="w-full" 
          disabled={isLoading}
        >
          {isLoading ? (
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
              注册中...
            </div>
          ) : (
            "注册"
          )}
        </Button>
      </form>
    </div>
  );
}
