"use client";

import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/toast";
import { authClient } from "@/lib/auth-client";
import { useState } from "react";
import Image from "next/image";

interface GitHubLoginButtonProps {
  callbackURL?: string;
  disabled?: boolean;
}

export function GitHubLoginButton({
  callbackURL = "/",
  disabled = false
}: GitHubLoginButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { addToast } = useToast();

  const handleGitHubLogin = async () => {
    try {
      setIsLoading(true);
      await authClient.signIn.social({
        provider: "github",
        callbackURL,
      });
    } catch (error) {
      console.error("GitHub 登录失败:", error);
      addToast({
        type: "error",
        title: "登录失败",
        description: "GitHub 登录过程中发生错误",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      onClick={handleGitHubLogin}
      disabled={disabled || isLoading}
      className="w-full flex items-center justify-center gap-2 "
      variant="outline"
    >
      {isLoading ? (
        <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
      ) : (
        <Image
          src="/images/github.svg"
          alt="GitHub"
          width={16}
          height={16}
          className="w-4 h-4"
        />
      )}
      使用 GitHub 登录
    </Button>
  );
}