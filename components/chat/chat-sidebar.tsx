'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { PlusIcon, MessageSquareIcon, PinIcon, MoreHorizontalIcon, TrashIcon, EditIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { useChats, useDeleteChat, useUpdateChat, type ChatWithPreview } from '@/hooks/use-chats';

interface ChatSidebarProps {
  currentChatId?: string;
  className?: string;
}

export function ChatSidebar({ currentChatId, className }: ChatSidebarProps) {
  const router = useRouter();
  const { data: chats = [] } = useChats();
  const deleteChat = useDeleteChat();
  const updateChat = useUpdateChat();
  const [editingChatId, setEditingChatId] = useState<string | null>(null);
  const [editTitle, setEditTitle] = useState('');

  const handleNewChat = () => {
    router.push('/chat');
  };

  const handleChatSelect = (chatId: string) => {
    router.push(`/chat/${chatId}`);
  };

  const handleDeleteChat = async (chatId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    if (confirm('确定要删除这个对话吗？')) {
      try {
        await deleteChat.mutateAsync(chatId);
        if (currentChatId === chatId) {
          router.push('/chat');
        }
      } catch (error) {
        console.error('删除对话失败:', error);
      }
    }
  };

  const handlePinChat = async (chatId: string, isPinned: boolean, event: React.MouseEvent) => {
    event.stopPropagation();
    try {
      await updateChat.mutateAsync({
        chatId,
        isPinned: !isPinned,
      });
    } catch (error) {
      console.error('固定对话失败:', error);
    }
  };

  const handleEditStart = (chat: ChatWithPreview, event: React.MouseEvent) => {
    event.stopPropagation();
    setEditingChatId(chat.id);
    setEditTitle(chat.title);
  };

  const handleEditSave = async (chatId: string) => {
    if (editTitle.trim()) {
      try {
        await updateChat.mutateAsync({
          chatId,
          title: editTitle.trim(),
        });
        setEditingChatId(null);
      } catch (error) {
        console.error('更新对话标题失败:', error);
      }
    }
  };

  const handleEditCancel = () => {
    setEditingChatId(null);
    setEditTitle('');
  };

  const formatDate = (date: Date) => {
    const now = new Date();
    const diffInHours = (now.getTime() - new Date(date).getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return '今天';
    } else if (diffInHours < 48) {
      return '昨天';
    } else if (diffInHours < 168) {
      return `${Math.floor(diffInHours / 24)}天前`;
    } else {
      return new Date(date).toLocaleDateString();
    }
  };

  // 分组聊天：固定的在前，然后按时间排序
  const pinnedChats = chats.filter(chat => chat.isPinned);
  const unpinnedChats = chats.filter(chat => !chat.isPinned);

  return (
    <div className={cn('flex flex-col h-full bg-muted/50', className)}>
      {/* 头部 */}
      <div className="p-4 border-b">
        <Button
          onClick={handleNewChat}
          className="w-full justify-start gap-2"
          variant="outline"
        >
          <PlusIcon size={16} />
          新建对话
        </Button>
      </div>

      {/* 聊天列表 */}
      <ScrollArea className="flex-1">
        <div className="p-2 space-y-1">
          {/* 固定的对话 */}
          {pinnedChats.length > 0 && (
            <div className="mb-4">
              <div className="px-2 py-1 text-xs font-medium text-muted-foreground">
                固定对话
              </div>
              {pinnedChats.map((chat) => (
                <ChatItem
                  key={chat.id}
                  chat={chat}
                  isActive={currentChatId === chat.id}
                  isEditing={editingChatId === chat.id}
                  editTitle={editTitle}
                  onSelect={() => handleChatSelect(chat.id)}
                  onDelete={(e) => handleDeleteChat(chat.id, e)}
                  onPin={(e) => handlePinChat(chat.id, chat.isPinned, e)}
                  onEditStart={(e) => handleEditStart(chat, e)}
                  onEditSave={() => handleEditSave(chat.id)}
                  onEditCancel={handleEditCancel}
                  onEditTitleChange={setEditTitle}
                  formatDate={formatDate}
                />
              ))}
            </div>
          )}

          {/* 普通对话 */}
          {unpinnedChats.length > 0 && (
            <div>
              {pinnedChats.length > 0 && (
                <div className="px-2 py-1 text-xs font-medium text-muted-foreground">
                  最近对话
                </div>
              )}
              {unpinnedChats.map((chat) => (
                <ChatItem
                  key={chat.id}
                  chat={chat}
                  isActive={currentChatId === chat.id}
                  isEditing={editingChatId === chat.id}
                  editTitle={editTitle}
                  onSelect={() => handleChatSelect(chat.id)}
                  onDelete={(e) => handleDeleteChat(chat.id, e)}
                  onPin={(e) => handlePinChat(chat.id, chat.isPinned, e)}
                  onEditStart={(e) => handleEditStart(chat, e)}
                  onEditSave={() => handleEditSave(chat.id)}
                  onEditCancel={handleEditCancel}
                  onEditTitleChange={setEditTitle}
                  formatDate={formatDate}
                />
              ))}
            </div>
          )}

          {chats.length === 0 && (
            <div className="text-center text-muted-foreground py-8">
              <MessageSquareIcon size={48} className="mx-auto mb-2 opacity-50" />
              <p>还没有对话</p>
              <p className="text-sm">点击上方按钮开始新对话</p>
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
}

// ChatItem 组件
interface ChatItemProps {
  chat: ChatWithPreview;
  isActive: boolean;
  isEditing: boolean;
  editTitle: string;
  onSelect: () => void;
  onDelete: (e: React.MouseEvent) => void;
  onPin: (e: React.MouseEvent) => void;
  onEditStart: (e: React.MouseEvent) => void;
  onEditSave: () => void;
  onEditCancel: () => void;
  onEditTitleChange: (title: string) => void;
  formatDate: (date: Date) => string;
}

function ChatItem({
  chat,
  isActive,
  isEditing,
  editTitle,
  onSelect,
  onDelete,
  onPin,
  onEditStart,
  onEditSave,
  onEditCancel,
  onEditTitleChange,
  formatDate,
}: ChatItemProps) {
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      onEditSave();
    } else if (e.key === 'Escape') {
      onEditCancel();
    }
  };

  return (
    <div
      className={cn(
        'group relative flex items-center gap-2 rounded-lg p-2 cursor-pointer hover:bg-accent/50 transition-colors',
        isActive && 'bg-accent'
      )}
      onClick={onSelect}
    >
      {/* 固定图标 */}
      {chat.isPinned && (
        <PinIcon size={12} className="text-muted-foreground flex-shrink-0" />
      )}

      {/* 聊天图标 */}
      <MessageSquareIcon size={16} className="text-muted-foreground flex-shrink-0" />

      {/* 聊天内容 */}
      <div className="flex-1 min-w-0">
        {isEditing ? (
          <input
            type="text"
            value={editTitle}
            onChange={(e) => onEditTitleChange(e.target.value)}
            onKeyDown={handleKeyDown}
            onBlur={onEditSave}
            className="w-full bg-transparent border-none outline-none text-sm"
            autoFocus
            onClick={(e) => e.stopPropagation()}
          />
        ) : (
          <>
            <div className="text-sm font-medium truncate">{chat.title}</div>
            {chat.lastMessage && (
              <div className="text-xs text-muted-foreground truncate">
                {chat.lastMessage}
              </div>
            )}
          </>
        )}
      </div>

      {/* 时间和操作按钮 */}
      <div className="flex items-center gap-1 flex-shrink-0">
        <div className="text-xs text-muted-foreground">
          {formatDate(chat.updatedAt)}
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={(e) => e.stopPropagation()}
            >
              <MoreHorizontalIcon size={12} />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={onEditStart}>
              <EditIcon size={14} className="mr-2" />
              重命名
            </DropdownMenuItem>
            <DropdownMenuItem onClick={onPin}>
              <PinIcon size={14} className="mr-2" />
              {chat.isPinned ? '取消固定' : '固定'}
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={onDelete}
              className="text-destructive focus:text-destructive"
            >
              <TrashIcon size={14} className="mr-2" />
              删除
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}