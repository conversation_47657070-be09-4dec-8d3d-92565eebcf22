'use client';

import { GlobeIcon } from 'lucide-react';
import {
  PromptInput,
  PromptInputButton,
  PromptInputModelSelect,
  PromptInputModelSelectContent,
  PromptInputModelSelectItem,
  PromptInputModelSelectTrigger,
  PromptInputModelSelectValue,
  PromptInputSubmit,
  PromptInputTextarea,
  PromptInputToolbar,
  PromptInputTools,
} from '@/components/ai-elements/prompt-input';
import type { ChatStatus } from 'ai';

const models = [
  {
    name: 'GPT 4o',
    value: 'openai/gpt-4o',
  },
  {
    name: 'Deepseek R1',
    value: 'deepseek/deepseek-r1',
  },
];

interface ChatInputProps {
  input: string;
  onInputChange: (value: string) => void;
  onSubmit: (e: React.FormEvent) => void;
  status: ChatStatus;
  model: string;
  onModelChange: (model: string) => void;
  webSearch: boolean;
  onWebSearchChange: (enabled: boolean) => void;
  disabled?: boolean;
  className?: string;
}

export function ChatInput({
  input,
  onInputChange,
  onSubmit,
  status,
  model,
  onModelChange,
  webSearch,
  onWebSearchChange,
  disabled = false,
  className,
}: ChatInputProps) {
  return (
    <PromptInput onSubmit={onSubmit} className={className}>
      <PromptInputTextarea
        onChange={(e) => onInputChange(e.target.value)}
        value={input}
        disabled={disabled || status !== 'ready'}
        placeholder="输入消息..."
      />
      <PromptInputToolbar>
        <PromptInputTools>
          <PromptInputButton
            variant={webSearch ? 'default' : 'ghost'}
            onClick={() => onWebSearchChange(!webSearch)}
            disabled={disabled}
          >
            <GlobeIcon size={16} />
            <span>搜索</span>
          </PromptInputButton>
          <PromptInputModelSelect
            onValueChange={onModelChange}
            value={model}
            disabled={disabled}
          >
            <PromptInputModelSelectTrigger>
              <PromptInputModelSelectValue />
            </PromptInputModelSelectTrigger>
            <PromptInputModelSelectContent>
              {models.map((modelOption) => (
                <PromptInputModelSelectItem
                  key={modelOption.value}
                  value={modelOption.value}
                >
                  {modelOption.name}
                </PromptInputModelSelectItem>
              ))}
            </PromptInputModelSelectContent>
          </PromptInputModelSelect>
        </PromptInputTools>
        <PromptInputSubmit
          disabled={!input.trim() || disabled || status !== 'ready'}
          status={status}
        />
      </PromptInputToolbar>
    </PromptInput>
  );
}