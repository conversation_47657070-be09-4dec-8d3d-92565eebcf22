'use client';

import {
  Conversation,
  ConversationContent,
  ConversationScrollButton,
} from '@/components/ai-elements/conversation';
import { Message, MessageContent } from '@/components/ai-elements/message';
import { Response } from '@/components/ai-elements/response';
import {
  Source,
  Sources,
  SourcesContent,
  SourcesTrigger,
} from '@/components/ai-elements/source';
import {
  Reasoning,
  ReasoningContent,
  ReasoningTrigger,
} from '@/components/ai-elements/reasoning';
import { Loader } from '@/components/ai-elements/loader';
import type { DBMessage } from '@/lib/db/schema';
import type { ChatStatus } from 'ai';

interface ChatContentProps {
  messages: DBMessage[];
  status?: ChatStatus;
  className?: string;
}

export function ChatContent({ messages, status, className }: ChatContentProps) {
  // 转换数据库消息格式为 UI 消息格式
  const uiMessages = messages.map((msg) => ({
    id: msg.id,
    role: msg.role as 'user' | 'assistant',
    parts: msg.parts as any[],
    createdAt: msg.createdAt,
  }));

  return (
    <Conversation className={className}>
      <ConversationContent>
        {uiMessages.map((message) => (
          <div key={message.id}>
            {/* 显示来源信息（仅对助手消息） */}
            {message.role === 'assistant' && (
              <Sources>
                {message.parts.map((part, i) => {
                  switch (part.type) {
                    case 'source-url':
                      return (
                        <>
                          <SourcesTrigger
                            key={`trigger-${message.id}-${i}`}
                            count={
                              message.parts.filter(
                                (part) => part.type === 'source-url',
                              ).length
                            }
                          />
                          <SourcesContent key={`content-${message.id}-${i}`}>
                            <Source
                              key={`source-${message.id}-${i}`}
                              href={part.url}
                              title={part.url}
                            />
                          </SourcesContent>
                        </>
                      );
                    default:
                      return null;
                  }
                })}
              </Sources>
            )}

            {/* 消息内容 */}
            <Message from={message.role} key={message.id}>
              <MessageContent>
                {message.parts.map((part, i) => {
                  switch (part.type) {
                    case 'text':
                      return (
                        <Response key={`text-${message.id}-${i}`}>
                          {part.text}
                        </Response>
                      );
                    case 'reasoning':
                      return (
                        <Reasoning
                          key={`reasoning-${message.id}-${i}`}
                          className="w-full"
                          isStreaming={status === 'streaming'}
                        >
                          <ReasoningTrigger />
                          <ReasoningContent>{part.text}</ReasoningContent>
                        </Reasoning>
                      );
                    default:
                      return null;
                  }
                })}
              </MessageContent>
            </Message>
          </div>
        ))}

        {/* 加载状态 */}
        {status === 'submitted' && <Loader />}
      </ConversationContent>

      {/* 滚动到底部按钮 */}
      <ConversationScrollButton />
    </Conversation>
  );
}