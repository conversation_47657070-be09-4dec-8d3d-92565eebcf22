{"name": "promptrepo2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "initdb": "npx drizzle-kit push", "lint": "next lint"}, "dependencies": {"@ai-sdk/react": "^2.0.8", "@icons-pack/react-simple-icons": "^13.7.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/react-use-controllable-state": "^1.2.2", "@tanstack/react-query": "^5.84.2", "@tanstack/react-query-devtools": "^5.84.2", "ai": "^5.0.8", "better-auth": "^1.3.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.2.1", "drizzle-orm": "^0.44.4", "embla-carousel-react": "^8.6.0", "harden-react-markdown": "^1.0.2", "katex": "^0.16.22", "lucide-react": "^0.539.0", "next": "15.4.6", "postgres": "^3.4.7", "react": "19.1.0", "react-dom": "19.1.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "rehype-katex": "^7.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "use-stick-to-bottom": "^1.1.1", "zod": "^3.25.76", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "drizzle-kit": "^0.31.4", "eslint": "^9", "eslint-config-next": "15.4.6", "tailwindcss": "^4", "tsx": "^4.20.3", "tw-animate-css": "^1.3.6", "typescript": "^5"}}