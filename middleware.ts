import { NextRequest, NextResponse } from "next/server";

// 需要认证的路由
const protectedRoutes = [
  "/chat",
  "/profile",
  "/settings",
];

// 认证相关的路由（已登录用户不应访问）
const authRoutes = [
  "/login",
  "/register",
];

// 公开路由（不需要认证）
// const publicRoutes = [
//   "/",
//   "/api/auth",
// ];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // 检查是否是 API 路由
  if (pathname.startsWith("/api/")) {
    return NextResponse.next();
  }

  // 简化的认证检查：通过 cookie 检查是否有会话
  const sessionCookie = request.cookies.get("better-auth.session_token");
  const isAuthenticated = !!sessionCookie?.value;

  // 如果是受保护的路由且用户未认证，重定向到登录页
  if (protectedRoutes.some(route => pathname.startsWith(route))) {
    if (!isAuthenticated) {
      const loginUrl = new URL("/login", request.url);
      loginUrl.searchParams.set("callbackUrl", pathname);
      return NextResponse.redirect(loginUrl);
    }
  }

  // 如果是认证路由且用户已认证，重定向到首页
  if (authRoutes.some(route => pathname.startsWith(route))) {
    if (isAuthenticated) {
      const callbackUrl = request.nextUrl.searchParams.get("callbackUrl") || "/";
      return NextResponse.redirect(new URL(callbackUrl, request.url));
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    "/((?!_next/static|_next/image|favicon.ico|public/).*)",
  ],
};
