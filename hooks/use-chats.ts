'use client';

import { useSuspenseQuery, useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import type { Chat, DBMessage } from '@/lib/db/schema';

// 聊天列表数据类型
export interface ChatWithPreview extends Chat {
  lastMessage: string;
  messageCount: number;
}

// 获取聊天列表
export function useChats() {
  return useSuspenseQuery({
    queryKey: ['chats'],
    queryFn: async (): Promise<ChatWithPreview[]> => {
      const response = await fetch('/api/chats');
      if (!response.ok) {
        throw new Error('获取聊天列表失败');
      }
      const data = await response.json();
      return data.chats;
    },
  });
}

// 条件性获取聊天列表
export function useChatsConditional(enabled: boolean = true) {
  return useQuery({
    queryKey: ['chats'],
    queryFn: async (): Promise<ChatWithPreview[]> => {
      const response = await fetch('/api/chats');
      if (!response.ok) {
        throw new Error('获取聊天列表失败');
      }
      const data = await response.json();
      return data.chats;
    },
    enabled,
  });
}

// 创建新聊天
export function useCreateChat() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: {
      id: string;
      title: string;
      visibility?: 'public' | 'private';
    }) => {
      const response = await fetch('/api/chats', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      });

      if (!response.ok) {
        throw new Error('创建聊天失败');
      }

      const data = await response.json();
      return data.chat;
    },
    onSuccess: () => {
      // 刷新聊天列表
      queryClient.invalidateQueries({ queryKey: ['chats'] });
    },
  });
}

// 更新聊天
export function useUpdateChat() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: {
      chatId: string;
      title?: string;
      visibility?: 'public' | 'private';
      isPinned?: boolean;
    }) => {
      const { chatId, ...updateData } = params;
      const response = await fetch(`/api/chats/${chatId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        throw new Error('更新聊天失败');
      }

      const data = await response.json();
      return data.chat;
    },
    onSuccess: (_, variables) => {
      // 刷新相关查询
      queryClient.invalidateQueries({ queryKey: ['chats'] });
      queryClient.invalidateQueries({ queryKey: ['chat', variables.chatId] });
    },
  });
}

// 删除聊天
export function useDeleteChat() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (chatId: string) => {
      const response = await fetch(`/api/chats/${chatId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('删除聊天失败');
      }

      return { chatId };
    },
    onSuccess: (data) => {
      // 刷新聊天列表并移除特定聊天的缓存
      queryClient.invalidateQueries({ queryKey: ['chats'] });
      queryClient.removeQueries({ queryKey: ['chat', data.chatId] });
      queryClient.removeQueries({ queryKey: ['messages', data.chatId] });
    },
  });
}

// 获取特定聊天详情
export function useChat(chatId: string) {
  return useSuspenseQuery({
    queryKey: ['chat', chatId],
    queryFn: async (): Promise<{ chat: Chat; messages: DBMessage[] }> => {
      if (!chatId) {
        throw new Error('Chat ID is required');
      }
      const response = await fetch(`/api/chats/${chatId}`);
      if (!response.ok) {
        throw new Error('获取聊天详情失败');
      }
      return response.json();
    },
  });
}

// 条件性获取聊天详情（用于可能未认证的情况）
export function useChatConditional(chatId: string, enabled: boolean = true) {
  return useQuery({
    queryKey: ['chat', chatId],
    queryFn: async (): Promise<{ chat: Chat; messages: DBMessage[] }> => {
      if (!chatId) {
        throw new Error('Chat ID is required');
      }
      const response = await fetch(`/api/chats/${chatId}`);
      if (!response.ok) {
        throw new Error('获取聊天详情失败');
      }
      return response.json();
    },
    enabled: enabled && !!chatId,
  });
}

// 获取聊天消息
export function useMessages(chatId: string) {
  return useSuspenseQuery({
    queryKey: ['messages', chatId],
    queryFn: async (): Promise<DBMessage[]> => {
      if (!chatId) {
        throw new Error('Chat ID is required');
      }
      const response = await fetch(`/api/chats/${chatId}/messages`);
      if (!response.ok) {
        throw new Error('获取消息列表失败');
      }
      const data = await response.json();
      return data.messages;
    },
  });
}

// 创建消息
export function useCreateMessage() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: {
      chatId: string;
      role: string;
      parts: any[];
      attachments?: any[];
      parentMessageId?: string;
      selectedModel?: string;
      selectedTool?: string;
      annotations?: any;
    }) => {
      const { chatId, ...messageData } = params;
      const response = await fetch(`/api/chats/${chatId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(messageData),
      });

      if (!response.ok) {
        throw new Error('创建消息失败');
      }

      const data = await response.json();
      return data.message;
    },
    onSuccess: (_, variables) => {
      // 刷新相关查询
      queryClient.invalidateQueries({ queryKey: ['messages', variables.chatId] });
      queryClient.invalidateQueries({ queryKey: ['chat', variables.chatId] });
      queryClient.invalidateQueries({ queryKey: ['chats'] });
    },
  });
}
