"use client";

import { useEffect, useState } from "react";
import { authClient } from "@/lib/auth-client";
import type { User, Session, AuthState } from "@/lib/types/auth";

export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    session: null,
    isLoading: true,
    isAuthenticated: false,
  });

  useEffect(() => {
    let mounted = true;

    const checkAuth = async () => {
      try {
        const { data } = await authClient.getSession();
        
        if (mounted) {
          if (data?.user && data?.session) {
            setAuthState({
              user: data.user,
              session: data.session,
              isLoading: false,
              isAuthenticated: true,
            });
          } else {
            setAuthState({
              user: null,
              session: null,
              isLoading: false,
              isAuthenticated: false,
            });
          }
        }
      } catch (error) {
        console.error("检查认证状态失败:", error);
        if (mounted) {
          setAuthState({
            user: null,
            session: null,
            isLoading: false,
            isAuthenticated: false,
          });
        }
      }
    };

    checkAuth();

    return () => {
      mounted = false;
    };
  }, []);

  const signOut = async () => {
    try {
      await authClient.signOut();
      setAuthState({
        user: null,
        session: null,
        isLoading: false,
        isAuthenticated: false,
      });
    } catch (error) {
      console.error("登出失败:", error);
    }
  };

  const refreshAuth = async () => {
    setAuthState(prev => ({ ...prev, isLoading: true }));
    
    try {
      const { data } = await authClient.getSession();
      
      if (data?.user && data?.session) {
        setAuthState({
          user: data.user,
          session: data.session,
          isLoading: false,
          isAuthenticated: true,
        });
      } else {
        setAuthState({
          user: null,
          session: null,
          isLoading: false,
          isAuthenticated: false,
        });
      }
    } catch (error) {
      console.error("刷新认证状态失败:", error);
      setAuthState({
        user: null,
        session: null,
        isLoading: false,
        isAuthenticated: false,
      });
    }
  };

  return {
    ...authState,
    signOut,
    refreshAuth,
  };
}
